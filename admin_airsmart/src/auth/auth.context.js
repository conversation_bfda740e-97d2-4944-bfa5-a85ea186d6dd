import React, { createContext, useContext, useState, useEffect } from 'react';
import { Box, CircularProgress } from '@mui/material';
import { keyframes } from '@mui/system';
import { auth } from '../firebase';
import { onAuthStateChanged } from 'firebase/auth';
import airsmartIcon from '../public/airsmart.svg';
// Define animations
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
`;

// Create auth context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      // Check if user has admin privileges
      if (user) {
        try {
          const idTokenResult = await user.getIdTokenResult();
          const role = idTokenResult.claims.role;
          
          // Only allow admin users to access admin panel
          if (role !== 'admin' && role !== 'architect') {
            console.warn('User does not have admin privileges');
            // You can redirect to unauthorized page or logout
          }
        } catch (error) {
          console.error('Error checking user role:', error);
        }
      }

      setLoading(false);
    });

    // Cleanup subscription
    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {loading ? (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100vh',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: (theme) => theme.palette.background.default,
          zIndex: 9999
        }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            animation: `${fadeIn} 1s ease-in-out`
          }}>
            <img
              src={airsmartIcon}
              alt="AirSmart Logo"
              style={{
                width: 80,
                height: 80,
                marginBottom: 24,
                animation: `${pulse} 1.5s infinite ease-in-out`
              }}
            />
            <CircularProgress
              color="primary"
              size={40}
              thickness={4}
              sx={{
                opacity: 0.8
              }}
            />
          </Box>
        </Box>
      ) : (
        children
      )}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  return useContext(AuthContext);
};