import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Paper,
  Avatar,
  Chip,
  CircularProgress,
  Divider,
  Container,
  Stack,
  useTheme,
  alpha
} from '@mui/material';
import {
  People as PeopleIcon,
  Description as FilesIcon,
  School as ModulesIcon,
  AdminPanelSettings as AdminIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useAuth } from '../auth/auth.context';
import { userService } from '../services/userService';

// Component tối ưu với thiết kế responsive
const StatCard = ({ title, value, icon, loading = false, color = 'primary' }) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        borderRadius: 3,
        border: '1px solid',
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${alpha(theme.palette[color].main, 0.05)} 0%, ${alpha(theme.palette[color].main, 0.02)} 100%)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-4px) scale(1.02)',
          boxShadow: `0 20px 40px ${alpha(theme.palette[color].main, 0.15)}`,
          borderColor: theme.palette[color].main,
          '& .stat-icon': {
            transform: 'scale(1.1) rotate(5deg)',
          }
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: `linear-gradient(90deg, ${theme.palette[color].main} 0%, ${theme.palette[color].light} 100%)`,
        }
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar 
            className="stat-icon"
            sx={{ 
              bgcolor: alpha(theme.palette[color].main, 0.1),
              color: theme.palette[color].main,
              width: { xs: 48, sm: 56 },
              height: { xs: 48, sm: 56 },
              transition: 'all 0.3s ease',
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography 
              color="text.secondary" 
              variant="body2" 
              fontWeight={500}
              sx={{ 
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                textTransform: 'uppercase',
                letterSpacing: 0.5,
                mb: 0.5
              }}
            >
              {title}
            </Typography>
            {loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={20} sx={{ color: theme.palette[color].main }} />
                <Typography variant="body2" color="text.secondary">Loading...</Typography>
              </Box>
            ) : (
              <Typography 
                variant="h4" 
                component="h2" 
                fontWeight={700}
                sx={{ 
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  color: 'text.primary',
                  lineHeight: 1
                }}
              >
                {value?.toLocaleString()}
              </Typography>
            )}
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};

const QuickActionCard = ({ title, description, buttonText, href, icon }) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        borderRadius: 3,
        border: '1px solid',
        borderColor: 'divider',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        display: 'flex',
        flexDirection: 'column',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          borderColor: theme.palette.primary.main,
          '& .action-icon': {
            transform: 'scale(1.1)',
            color: theme.palette.primary.main,
          },
          '& .action-button': {
            transform: 'translateY(-2px)',
          }
        }
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 }, flex: 1 }}>
        <Stack spacing={2}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              className="action-icon"
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 40,
                height: 40,
                transition: 'all 0.3s ease'
              }}
            >
              {icon}
            </Avatar>
            <Typography 
              variant="h6" 
              fontWeight={600}
              sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
            >
              {title}
            </Typography>
          </Box>
          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ 
              lineHeight: 1.6,
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            {description}
          </Typography>
        </Stack>
      </CardContent>
      <Divider />
      <CardActions sx={{ p: { xs: 2, sm: 3 }, pt: 2 }}>
        <Button 
          className="action-button"
          variant="outlined"
          href={href}
          fullWidth
          sx={{ 
            borderRadius: 2,
            py: 1,
            fontWeight: 600,
            textTransform: 'none',
            fontSize: { xs: '0.875rem', sm: '1rem' },
            transition: 'all 0.3s ease'
          }}
        >
          {buttonText}
        </Button>
      </CardActions>
    </Card>
  );
};

export default function Dashboard() {
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalModules: 0,
  });
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Get current user role
        if (currentUser) {
          const idTokenResult = await currentUser.getIdTokenResult();
          setUserRole(idTokenResult.claims.role || 'user');
        }

        // Fetch user statistics
        const usersData = await userService.getAllUsers({ limit: 1000 });
        
        const totalUsers = usersData.totalUsers || usersData.users?.length || 0;
        const adminUsers = usersData.users?.filter(user => 
          user.role === 'admin' || user.role === 'architect'
        ).length || 0;

        setStats({
          totalUsers,
          adminUsers
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentUser]);

  const getUserDisplayName = () => {
    if (currentUser?.displayName) {
      return currentUser.displayName;
    }
    if (currentUser?.email) {
      return currentUser.email.split('@')[0];
    }
    return 'Admin';
  };

  // const quickActions = [
  //   {
  //     title: 'User Management',
  //     description: 'Manage users, roles, and permissions for your organization. Create new accounts and set access levels.',
  //     buttonText: 'Manage Users',
  //     href: '/users',
  //     icon: <PeopleIcon />
  //   },
  //   {
  //     title: 'File Management',
  //     description: 'Upload and manage training materials, documentation, and resources. Organize files by courses.',
  //     buttonText: 'Manage Files',
  //     href: '/files',
  //     icon: <FilesIcon />
  //   },
  //   {
  //     title: 'Module Management',
  //     description: 'Create and manage interactive training modules with quizzes and step-by-step guides for users.',
  //     buttonText: 'Manage Modules',
  //     href: '/modules',
  //     icon: <ModulesIcon />
  //   },
  //   {
  //     title: 'System Analytics',
  //     description: 'Monitor system performance, user engagement, and generate detailed reports for insights.',
  //     buttonText: 'View Analytics',
  //     href: '/analytics',
  //     icon: <TrendingUpIcon />
  //   }
  // ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Welcome Section */}
      <Paper 
        elevation={0}
        sx={{ 
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
          borderBottom: '1px solid',
          borderColor: 'divider',
          borderRadius: 0,
          py: { xs: 3, sm: 4 },
          px: { xs: 2, sm: 3 }
        }}
      >
        <Container maxWidth="xl">
          <Stack 
            direction={{ xs: 'column', sm: 'row' }} 
            alignItems={{ xs: 'flex-start', sm: 'center' }} 
            justifyContent="space-between"
            spacing={2}
          >
            <Box>
              <Typography 
                variant="h3" 
                fontWeight={700} 
                gutterBottom
                sx={{ 
                  fontSize: { xs: '1.75rem', sm: '2.5rem', md: '3rem' },
                  lineHeight: 1.2,
                  background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Welcome back, {getUserDisplayName()}
              </Typography>
              <Typography 
                variant="h6" 
                color="text.secondary"
                sx={{ 
                  fontSize: { xs: '1rem', sm: '1.25rem' },
                  fontWeight: 400
                }}
              >
                AirSmart Admin Panel Dashboard
              </Typography>
            </Box>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Chip 
                label={userRole.toUpperCase()} 
                size="medium"
                sx={{ 
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                  border: '1px solid',
                  borderColor: alpha(theme.palette.primary.main, 0.3),
                  px: 1
                }}
              />
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  width: { xs: 40, sm: 48 },
                  height: { xs: 40, sm: 48 }
                }}
              >
                <DashboardIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
              </Avatar>
            </Stack>
          </Stack>
        </Container>
      </Paper>

      <Container maxWidth="xl" sx={{ py: { xs: 3, sm: 4 } }}>
        
        <Box sx={{ mb: { xs: 4, sm: 6 } }}>
          <Typography 
            variant="h5" 
            fontWeight={600} 
            gutterBottom
            sx={{ 
              mb: 3,
              fontSize: { xs: '1.25rem', sm: '1.5rem' }
            }}
          >
            Overview Statistics
          </Typography>
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} sm={6} lg={3}>
              <StatCard
                title="Total Users"
                value={stats.totalUsers}
                icon={<PeopleIcon />}
                loading={loading}
                color="primary"
              />
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <StatCard
                title="Admin Users"
                value={stats.adminUsers}
                icon={<AdminIcon />}
                loading={loading}
                color="secondary"
              />
            </Grid>
        
          </Grid>
        </Box>
{/* 
        <Box>
          <Typography 
            variant="h5" 
            fontWeight={600} 
            gutterBottom
            sx={{ 
              mb: 3,
              fontSize: { xs: '1.25rem', sm: '1.5rem' }
            }}
          >
            Quick Actions
          </Typography>
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} lg={6} xl={3} key={index}>
                <QuickActionCard {...action} />
              </Grid>
            ))}
          </Grid>
        </Box> */}
      </Container>
    </Box>
  );
}