import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Fab,
  Slider,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add as AddIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Notes as AnnotationIcon,
  Timeline as TimelineIcon,
  LiveHelp as TutorialIcon,
  CloudUpload,
  ZoomIn,
  ZoomOut,
  CenterFocusWeak,
} from '@mui/icons-material';

import adminModuleService from '../services/adminModuleService';
import { convertToSignedUrl } from '../utils/wasabiHelper';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`image-tabpanel-${index}`}
      aria-labelledby={`image-tab-${index}`}
      style={{ padding: '16px 0' }}
      {...other}
    >
      {value === index && (
        <Box>{children}</Box>
      )}
    </div>
  );
}

// Image Viewer Component with annotations and zoom
function ImageViewer({ 
  imageUrl, 
  imageAnnotations = [], 
  zoom = 1, 
  onZoomChange, 
  onAnnotationClick, 
  onImageClick,
  showAnnotations = true, // Thêm prop để control hiển thị annotations
  enableDrag = true // Thêm prop để control drag functionality
}) {
  const containerRef = useRef(null);
  const [dragStart, setDragStart] = useState(null);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const handleMouseDown = (e) => {
    if (e.button !== 0 || !enableDrag) return; // Chỉ handle drag khi enableDrag = true
    setDragStart({ x: e.clientX - offset.x, y: e.clientY - offset.y });
    setIsDragging(true);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !dragStart || !enableDrag) return;
    setOffset({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragStart(null);
  };

  const handleImageClick = (e) => {
    if (isDragging || !onImageClick) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    
    onImageClick({ x, y });
  };

  useEffect(() => {
    if (isDragging && enableDrag) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart, enableDrag]);

  return (
    <Box 
      ref={containerRef}
      sx={{ 
        position: 'relative', 
        width: '100%', 
        height: '100%',
        overflow: 'hidden',
        cursor: !enableDrag ? 'crosshair' : (isDragging ? 'grabbing' : 'grab')
      }}
      onMouseDown={handleMouseDown}
    >
      {imageUrl ? (
        <>
          <img
            src={imageUrl}
            alt="Topic Image"
            style={{
              position: 'absolute',
              transform: `translate(${offset.x}px, ${offset.y}px) scale(${zoom})`,
              transformOrigin: 'center center',
              maxWidth: 'none',
              maxHeight: 'none',
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              userSelect: 'none',
              pointerEvents: isDragging ? 'none' : 'auto'
            }}
            onClick={handleImageClick}
            draggable={false}
          />
          
          {/* Image Annotations Overlay - chỉ hiển thị khi showAnnotations = true */}
          {showAnnotations && imageAnnotations.map((annotation, index) => (
            <Box
              key={annotation.id || index}
              sx={{
                position: 'absolute',
                left: `${annotation.position.x}%`,
                top: `${annotation.position.y}%`,
                transform: `translate(-50%, -50%) scale(${1/zoom})`,
                transformOrigin: 'center center',
                zIndex: 10,
                pointerEvents: 'auto'
              }}
            >
              <Button
                variant="contained"
                size="small"
                color={annotation.style?.color || 'primary'}
                onClick={(e) => {
                  e.stopPropagation();
                  onAnnotationClick?.(annotation);
                }}
                sx={{
                  minWidth: 'auto',
                  borderRadius: '50%',
                  width: 32,
                  height: 32,
                  fontSize: '12px',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    transition: 'transform 0.2s'
                  }
                }}
              >
                {index + 1}
              </Button>
              
              {/* Annotation tooltip */}
              {annotation.title && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: '-40px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: 'rgba(0,0,0,0.8)',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    whiteSpace: 'nowrap',
                    opacity: 0,
                    transition: 'opacity 0.2s',
                    pointerEvents: 'none',
                    '&:hover': {
                      opacity: 1
                    }
                  }}
                  className="annotation-tooltip"
                >
                  {annotation.title}
                </Box>
              )}
            </Box>
          ))}
        </>
      ) : (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#f5f5f5',
            border: '2px dashed #ccc',
            cursor: 'pointer'
          }}
        >
          <CloudUpload sx={{ fontSize: 60, color: '#999', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            Upload Image File
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Drag and drop or click to select image
          </Typography>
        </Box>
      )}
    </Box>
  );
}

// Loading component
function LoadingOverlay() {
  return (
    <Box sx={{ 
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        Loading Image...
      </Typography>
    </Box>
  );
}

function ImageStepEditor() {
  const location = useLocation();
  const navigate = useNavigate();
  const { moduleId, stepId, moduleData } = location.state || {};
  
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [imageUrl, setImageUrl] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [zoom, setZoom] = useState(1);
  const fileInputRef = useRef(null);

  // Topic data state
  const [stepData, setStepData] = useState({
    title: '',
    content: '',
    // Remove order initialization - let backend auto-calculate
    autoPlay: false,
    tags: [],
    stepType: 'image'
  });

  // Image-specific state
  const [imageFile, setImageFile] = useState(null);
  const [imageAnnotations, setImageAnnotations] = useState([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);

  // Load existing topic data if editing
  useEffect(() => {
    if (!moduleId) {
      navigate('/modules');
      return;
    }
    
    const loadStepData = async () => {
      setLoading(true);
      try {
        if (stepId) {
          const stepResponse = await adminModuleService.getStep(moduleId, stepId);
          if (stepResponse) {
            setStepData({
              title: stepResponse.title || '',
              content: stepResponse.content || '',
              order: stepResponse.order || 1,
              autoPlay: stepResponse.autoPlay || false,
              tags: stepResponse.tags || [],
              stepType: 'image'
            });
            
            if (stepResponse.imageUrl) {
              setImageUrl(stepResponse.imageUrl);
            }
            
            if (stepResponse.imageAnnotations && Array.isArray(stepResponse.imageAnnotations)) {
              setImageAnnotations(stepResponse.imageAnnotations);
            }
          }
        }
      } catch (error) {
        console.error('Error loading topic data:', error);
        setSnackbar({
          open: true,
          message: `Error loading data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadStepData();
  }, [moduleId, stepId, navigate]);

  // Handle image file selection
  const handleImageFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setSnackbar({
        open: true,
        message: 'Please select a valid image file (JPEG, PNG, GIF, WebP)',
        severity: 'error'
      });
      return;
    }

    setImageFile(file);
    const imageUrl = URL.createObjectURL(file);
    setImageUrl(imageUrl);
    
    console.log('Image file selected:', file.name, 'Size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
  };

  // Handle image annotation click on image
  const handleImageClick = (position) => {
    if (tabIndex !== 1) return; // Only add annotations on Annotations tab

    const newAnnotation = {
      id: `annotation_${Date.now()}`,
      position: position,
      title: `Annotation ${imageAnnotations.length + 1}`,
      description: '',
      action: 'info',
      style: {
        color: 'primary',
        size: 'medium'
      }
    };

    setImageAnnotations([...imageAnnotations, newAnnotation]);
    setSelectedAnnotation(newAnnotation);
    
    setSnackbar({
      open: true,
      message: 'Annotation added! Edit it in the panel on the right.',
      severity: 'success'
    });
  };

  // Handle zoom controls
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom * 1.2, 5));
  };

  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom / 1.2, 0.1));
  };

  const handleZoomReset = () => {
    setZoom(1);
  };

  // Handle save topic - Enhanced với upload image
  const handleSaveStep = async () => {
    try {
      setUploading(true);
      setUploadProgress(0);

      // Validate required fields
      if (!stepData.title.trim()) {
        throw new Error('Topic title is required');
      }
      if (!stepData.content.trim()) {
        throw new Error('Topic content is required');
      }

      console.log('🔄 Saving image topic...');

      let result;

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      if (imageFile) {
        // If there's a new image file, upload it
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('stepData', JSON.stringify({
          ...stepData,
          // Don't include order for new steps - let backend auto-calculate
          imageAnnotations: imageAnnotations
        }));

        if (stepId) {
          // Update existing topic with new image
          result = await adminModuleService.updateStepWithImage(moduleId, stepId, formData);
        } else {
          // Create new topic with image
          result = await adminModuleService.createStepWithImage(moduleId, formData);
        }
      } else {
        // No new image file, use regular update/create
        const completeStepData = {
          ...stepData,
          // Don't include order for new steps - let backend auto-calculate
          imageUrl: imageUrl,
          imageAnnotations: imageAnnotations
        };

        if (stepId) {
          result = await adminModuleService.updateStep(moduleId, stepId, completeStepData);
        } else {
          result = await adminModuleService.createStep(moduleId, completeStepData);
        }
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      console.log('✅ Image topic saved successfully:', result);

      setSnackbar({
        open: true,
        message: stepId ? 'Image topic updated successfully!' : 'Image topic created successfully!',
        severity: 'success'
      });
      
      setTimeout(() => {
        navigate('/modules');
      }, 1500);

    } catch (error) {
      console.error('❌ Error saving image topic:', error);
      setSnackbar({
        open: true,
        message: `Error saving topic: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <Box sx={{ height: 'calc(100vh - 88px)', overflow: 'hidden' }}>
      {/* Header/Toolbar */}
      <Paper sx={{ mb: 2 }} elevation={2}>
        <Toolbar>
          <IconButton edge="start" color="inherit" onClick={() => navigate('/modules')}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            {stepId ? 'Edit Image Topic' : 'Create New Image Topic'}
          </Typography>
          <Button 
            variant="contained"
            color="success"
            startIcon={<Save />}
            onClick={handleSaveStep}
            disabled={loading || !stepData.title || !stepData.content}
          >
            Save Topic
          </Button>
        </Toolbar>
      </Paper>

      {/* Main Content */}
      <Box sx={{ 
        display: 'flex', 
        height: 'calc(100% - 80px)', 
        gap: 1,
        px: 2
      }}>
        {/* Image Preview Panel - Left Side */}
        <Box sx={{ 
          flex: '1 1 75%',
          height: '100%',
          minWidth: 0
        }}>
          <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={3}>
            {loading && !imageUrl && <LoadingOverlay />}
            
            {/* Image Viewer */}
            <ImageViewer
              imageUrl={imageUrl}
              imageAnnotations={imageAnnotations}
              zoom={zoom}
              onZoomChange={setZoom}
              onAnnotationClick={setSelectedAnnotation}
              onImageClick={handleImageClick}
              showAnnotations={tabIndex === 1} // Chỉ hiển thị annotations khi ở tab Annotations (index 1)
              enableDrag={tabIndex !== 1} // Disable drag khi ở tab Annotations để dễ click thêm annotations
            />
            
            {/* Image Controls Toolbar */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}>
              <Tooltip title="Upload Image">
                <Fab 
                  size="small" 
                  color="primary" 
                  onClick={() => fileInputRef.current?.click()}
                >
                  <ImageIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Add Annotation (Click on image)">
                <Fab size="small" color="secondary">
                  <AnnotationIcon />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Zoom Controls */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              right: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 1
            }}>
              <Tooltip title="Zoom In">
                <Fab size="small" onClick={handleZoomIn}>
                  <ZoomIn />
                </Fab>
              </Tooltip>
              <Tooltip title="Zoom Out">
                <Fab size="small" onClick={handleZoomOut}>
                  <ZoomOut />
                </Fab>
              </Tooltip>
              <Tooltip title="Reset Zoom">
                <Fab size="small" onClick={handleZoomReset}>
                  <CenterFocusWeak />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Image Info */}
            <Box sx={{ 
              position: 'absolute', 
              bottom: 16, 
              left: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Zoom: {Math.round(zoom * 100)}%
              </Typography>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Annotations: {imageAnnotations.length}
              </Typography>
            </Box>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              onChange={handleImageFileSelect}
            />
          </Paper>
        </Box>

        {/* Step Editor Panel - Right Side */}
        <Box sx={{ 
          flex: '1 1 25%',
          height: '100%',
          minWidth: '320px',
          maxWidth: '400px'
        }}>
          <Paper sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} elevation={3}>
            {/* Tabs Navigation */}
            <AppBar position="static" color="default" elevation={0} sx={{ flexShrink: 0 }}>
              <Tabs
                value={tabIndex}
                onChange={(e, newValue) => setTabIndex(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',  
                    fontSize: '0.75rem',
                    padding: '6px 4px'
                  }
                }}
              >
                <Tab label="Basic" icon={<ImageIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Annotations" icon={<AnnotationIcon fontSize="small" />} iconPosition="top" />
                {/* Tab 3: Actions - Disabled */}
                <Tab label="Actions" icon={<TimelineIcon fontSize="small" />} iconPosition="top" disabled />
                {/* Tab 4: Interactive Tutorial - Disabled */}
                <Tab label="Tutorial" icon={<TutorialIcon fontSize="small" />} iconPosition="top" disabled />
              </Tabs>
            </AppBar>

            {/* Tab Content */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
              {/* Tab 1: Basic Information */}
              <TabPanel value={tabIndex} index={0}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                  <TextField
                    label="Topic Title"
                    value={stepData.title}
                    onChange={(e) => setStepData({ ...stepData, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                  />
                  
                  <TextField
                    label="Content"
                    value={stepData.content}
                    onChange={(e) => setStepData({ ...stepData, content: e.target.value })}
                    multiline
                    rows={3}
                    fullWidth
                    required
                    placeholder="Detailed topic instructions. This will be displayed to the user."
                    size="small"
                  />


                  <TextField
                    label="Tags (comma separated)"
                    value={stepData.tags ? stepData.tags.join(', ') : ''}
                    onChange={(e) => setStepData({
                      ...stepData,
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    fullWidth
                    placeholder="e.g. image, diagram, important"
                    size="small"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={stepData.autoPlay || false}
                        onChange={(e) => setStepData({ ...stepData, autoPlay: e.target.checked })}
                        size="small"
                      />
                    }
                    label="Auto-highlight annotations when topic is loaded"
                  />
                  
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="caption">
                      💡 Click on the image to add annotations. Switch to the "Annotations" tab to edit them.
                    </Typography>
                  </Alert>
                </Box>
              </TabPanel>
              
              {/* Tab 2: Image Annotations */}
              <TabPanel value={tabIndex} index={1}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Annotations ({imageAnnotations.length})
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        const newAnnotation = {
                          id: `annotation_${Date.now()}`,
                          position: { x: 50, y: 50 },
                          title: `Annotation ${imageAnnotations.length + 1}`,
                          description: '',
                          action: 'info',
                          style: {
                            color: 'primary',
                            size: 'medium'
                          }
                        };
                        setImageAnnotations([...imageAnnotations, newAnnotation]);
                        setSelectedAnnotation(newAnnotation);
                      }}
                    >
                      Add Annotation
                    </Button>
                  </Box>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Click on the image to add annotations. They will appear as numbered markers.
                  </Alert>
                  
                  <List>
                    {imageAnnotations.map((annotation, index) => (
                      <ListItem 
                        key={annotation.id || index}
                        button
                        selected={selectedAnnotation?.id === annotation.id}
                        onClick={() => setSelectedAnnotation(annotation)}
                      >
                        <ListItemText 
                          primary={annotation.title}
                          secondary={`Position: ${Math.round(annotation.position.x)}%, ${Math.round(annotation.position.y)}%`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => {
                              setImageAnnotations(imageAnnotations.filter(a => a.id !== annotation.id));
                              if (selectedAnnotation?.id === annotation.id) {
                                setSelectedAnnotation(null);
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  
                  {imageAnnotations.length === 0 && (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
                      <Typography color="text.secondary">
                        No annotations added yet. Click on the image to add one.
                      </Typography>
                    </Box>
                  )}
                  
                  {selectedAnnotation && (
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Edit Annotation
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                          <TextField
                            label="Title"
                            value={selectedAnnotation.title}
                            onChange={(e) => {
                              const updatedAnnotations = imageAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, title: e.target.value } 
                                  : a
                              );
                              setImageAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, title: e.target.value });
                            }}
                            fullWidth
                            size="small"
                          />
                          
                          <TextField
                            label="Description"
                            value={selectedAnnotation.description || ''}
                            onChange={(e) => {
                              const updatedAnnotations = imageAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, description: e.target.value } 
                                  : a
                              );
                              setImageAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, description: e.target.value });
                            }}
                            multiline
                            rows={3}
                            fullWidth
                            size="small"
                          />
                          
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <TextField
                                label="X Position %"
                                type="number"
                                value={Math.round(selectedAnnotation.position.x)}
                                onChange={(e) => {
                                  const x = Math.max(0, Math.min(100, parseFloat(e.target.value) || 0));
                                  const updatedAnnotations = imageAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, position: { ...a.position, x } } 
                                      : a
                                  );
                                  setImageAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ 
                                    ...selectedAnnotation, 
                                    position: { ...selectedAnnotation.position, x }
                                  });
                                }}
                                fullWidth
                                size="small"
                                inputProps={{ min: 0, max: 100 }}
                              />
                            </Grid>
                            <Grid item xs={6}>
                              <TextField
                                label="Y Position %"
                                type="number"
                                value={Math.round(selectedAnnotation.position.y)}
                                onChange={(e) => {
                                  const y = Math.max(0, Math.min(100, parseFloat(e.target.value) || 0));
                                  const updatedAnnotations = imageAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, position: { ...a.position, y } } 
                                      : a
                                  );
                                  setImageAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ 
                                    ...selectedAnnotation, 
                                    position: { ...selectedAnnotation.position, y }
                                  });
                                }}
                                fullWidth
                                size="small"
                                inputProps={{ min: 0, max: 100 }}
                              />
                            </Grid>
                          </Grid>
                          
                          <FormControl fullWidth size="small">
                            <InputLabel>Annotation Color</InputLabel>
                            <Select
                              value={selectedAnnotation.style?.color || 'primary'}
                              label="Annotation Color"
                              onChange={(e) => {
                                const updatedAnnotations = imageAnnotations.map(a => 
                                  a.id === selectedAnnotation.id 
                                    ? { ...a, style: { ...a.style, color: e.target.value } } 
                                    : a
                                );
                                setImageAnnotations(updatedAnnotations);
                                setSelectedAnnotation({ 
                                  ...selectedAnnotation, 
                                  style: { ...selectedAnnotation.style, color: e.target.value }
                                });
                              }}
                            >
                              <MenuItem value="primary">Blue (Primary)</MenuItem>
                              <MenuItem value="secondary">Purple (Secondary)</MenuItem>
                              <MenuItem value="error">Red (Error)</MenuItem>
                              <MenuItem value="warning">Orange (Warning)</MenuItem>
                              <MenuItem value="success">Green (Success)</MenuItem>
                            </Select>
                          </FormControl>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </TabPanel>
              
              {/* Tab 3: Actions - Disabled */}
              <TabPanel value={tabIndex} index={2}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Actions functionality has been disabled
                </Typography>
              </TabPanel>
              
              {/* Tab 4: Interactive Tutorial - Disabled */}
              <TabPanel value={tabIndex} index={3}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Interactive tutorial functionality has been disabled
                </Typography>
              </TabPanel>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Upload Progress */}
      {uploading && (
        <Box sx={{ 
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          background: 'rgba(255, 255, 255, 0.95)',
          padding: 3,
          borderRadius: 2,
          minWidth: '300px',
          textAlign: 'center',
          boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
        }}>
          <Typography variant="h6" gutterBottom>
            {stepId ? 'Updating Image Topic...' : 'Creating Image Topic...'}
          </Typography>
          <Typography variant="body2" gutterBottom sx={{ mb: 2 }}>
            {imageFile ? `Uploading ${imageFile.name}...` : 'Processing topic data...'}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={uploadProgress} 
            sx={{ mb: 2, height: 8, borderRadius: 4 }}
          />
          <Typography variant="body2" color="text.secondary">
            {uploadProgress}% complete
          </Typography>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ImageStepEditor;