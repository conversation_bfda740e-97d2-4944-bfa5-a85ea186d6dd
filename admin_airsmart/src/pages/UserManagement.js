import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  Snackbar,
  Tooltip,
  InputAdornment,
  Stack,
  CircularProgress,
  Grid,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Build as BuildIcon,
  Engineering as EngineeringIcon,
  Architecture as ArchitectureIcon,
  Sell as SellIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  FirstPage as FirstPageIcon,
  LastPage as LastPageIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import userService from '../services/userService';
import adminModuleService from '../services/adminModuleService';

function UserManagement() {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    installer: 0,
    engineer: 0,
    architect: 0,
    salesperson: 0,
  });

  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    role: 'installer',
    password: '',
  });

  const [openCourseLockDialog, setOpenCourseLockDialog] = useState(false);
  const [selectedUserForCourses, setSelectedUserForCourses] = useState(null);
  const [courses, setCourses] = useState([]);
  const [userLockedCourses, setUserLockedCourses] = useState([]);
  const [coursesLoading, setCoursesLoading] = useState(false);
  const [courseLockLoading, setCourseLockLoading] = useState({});
  const [savingLockedCourses, setSavingLockedCourses] = useState(false);

  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        role: roleFilter !== 'all' ? roleFilter : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const response = await userService.getAllUsers(params);
      
      setUsers(response.users || []);
      setFilteredUsers(response.users || []);
      setStats(response.stats || stats);
      
    } catch (error) {
      console.error('Error loading users:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load users. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchTerm, roleFilter]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const clearFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
    setPage(0);
  };

  const handleOpenDialog = (user = null) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        displayName: user.displayName || '',
        email: user.email || '',
        role: user.role || 'installer',
        password: '', // Don't pre-fill password for editing
      });
    } else {
      setEditingUser(null);
      setFormData({
        displayName: '',
        email: '',
        role: 'installer',
        password: '',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingUser(null);
  };

  const handleSaveUser = async () => {
    try {
      setLoading(true);
      
      if (editingUser) {
        // Update existing user
        await userService.updateUser(editingUser.uid, {
          displayName: formData.displayName,
          email: formData.email,
          role: formData.role,
        });
        setSnackbar({
          open: true,
          message: 'User updated successfully!',
          severity: 'success'
        });
      } else {
        // Add new user - password is required for new users
        if (!formData.password) {
          setSnackbar({
            open: true,
            message: 'Password is required for new users',
            severity: 'error'
          });
          return;
        }
        await userService.createUser(formData);
        setSnackbar({
          open: true,
          message: 'User created successfully!',
          severity: 'success'
        });
      }
      
      handleCloseDialog();
      loadUsers(); // Reload users after save
      
    } catch (error) {
      console.error('Save error:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to save user. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenMenu = (event, user) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const exportUsers = () => {
    const csvContent = [
      ['UID', 'Display Name', 'Email', 'Role', 'Created At', 'Updated At'],
      ...filteredUsers.map(user => [
        user.uid,
        user.displayName || '',
        user.email || '',
        user.role || '',
        user.createdAt || '',
        user.updatedAt || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `users_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'installer': return 'primary';
      case 'engineer': return 'warning';
      case 'architect': return 'info';
      case 'salesperson': return 'success';
      default: return 'default';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'installer': return <BuildIcon />;
      case 'engineer': return <EngineeringIcon />;
      case 'architect': return <ArchitectureIcon />;
      case 'salesperson': return <SellIcon />;
      default: return <PersonIcon />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  // NEW: Open delete confirmation dialog
  const handleOpenDeleteDialog = (user) => {
    setUserToDelete(user);
    setOpenDeleteDialog(true);
    handleCloseMenu(); // Close the action menu
  };

  // NEW: Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setUserToDelete(null);
  };

  // UPDATED: Confirm and execute delete
  const handleConfirmDelete = async () => {
    if (!userToDelete) return;
    
    try {
      await userService.deleteUser(userToDelete.uid);
      setSnackbar({
        open: true,
        message: 'User deleted successfully!',
        severity: 'info'
      });
      handleCloseDeleteDialog();
      loadUsers(); // Reload users after delete
    } catch (error) {
      console.error('Delete error:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to delete user. Please try again.',
        severity: 'error'
      });
    }
  };

  // NEW: Load courses for locking
  const loadCourses = useCallback(async () => {
    setCoursesLoading(true);
    try {
      const response = await adminModuleService.getCourses();
      console.log('📋 Courses response:', response); // Debug log
      
      // Handle different response structures
      let coursesArray = [];
      if (response && response.data) {
        coursesArray = response.data;
      } else if (Array.isArray(response)) {
        coursesArray = response;
      } else {
        console.warn('Unexpected courses response structure:', response);
      }
      
      setCourses(coursesArray);
    } catch (error) {
      console.error('Error loading courses:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load courses. Please try again.',
        severity: 'error'
      });
    } finally {
      setCoursesLoading(false);
    }
  }, []);

  // NEW: Open course lock dialog
  const handleOpenCourseLockDialog = (user) => {
    setSelectedUserForCourses(user);
    setOpenCourseLockDialog(true);
    handleCloseMenu(); // Close the action menu
  };

  // NEW: Close course lock dialog
  const handleCloseCourseLockDialog = () => {
    setOpenCourseLockDialog(false);
    setSelectedUserForCourses(null);
    setUserLockedCourses([]);
  };

  // NEW: Load user locked courses
  const loadUserLockedCourses = useCallback(async (userId) => {
    setCoursesLoading(true);
    try {
      // Use userService instead of adminModuleService for user-related operations
      const response = await userService.getUserLockedCourses(userId);
      console.log('🔒 User locked courses response:', response); // Debug log
      
      // Handle different response structures
      let lockedCoursesArray = [];
      if (response && response.lockedCourses) {
        lockedCoursesArray = response.lockedCourses;
      } else if (response && response.courses) {
        lockedCoursesArray = response.courses;
      } else if (response && response.data && response.data.lockedCourses) {
        lockedCoursesArray = response.data.lockedCourses;
      } else if (response && response.data && response.data.courses) {
        lockedCoursesArray = response.data.courses;
      } else if (Array.isArray(response)) {
        lockedCoursesArray = response;
      } else {
        console.warn('Unexpected Coming Soon courses response structure:', response);
      }
      
      console.log('🔒 Final Coming Soon courses array:', lockedCoursesArray);
      setUserLockedCourses(lockedCoursesArray);
    } catch (error) {
      console.error('Error loading user Coming Soon courses:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load Coming Soon courses. Please try again.',
        severity: 'error'
      });
    } finally {
      setCoursesLoading(false);
    }
  }, []);

  // NEW: Handle course lock/unlock
  const handleCourseLockToggle = (courseId) => {
    if (!selectedUserForCourses) return;
    setUserLockedCourses(prev => {
      if (prev.includes(courseId)) {
        return prev.filter(id => id !== courseId);
      } else {
        return [...prev, courseId];
      }
    });
  };

  // NEW: Load user locked courses when dialog opens
  useEffect(() => {
    if (openCourseLockDialog && selectedUserForCourses) {
      loadCourses();
      loadUserLockedCourses(selectedUserForCourses.uid);
    }
  }, [openCourseLockDialog, selectedUserForCourses, loadCourses, loadUserLockedCourses]);

  // NEW: Handle save locked courses
  const handleSaveLockedCourses = async () => {
    if (!selectedUserForCourses) return;
    setSavingLockedCourses(true);
    try {
      await adminModuleService.updateUserLockedCourses(selectedUserForCourses.uid, userLockedCourses);
      setSnackbar({
        open: true,
        message: 'Coming Soon courses updated successfully!',
        severity: 'success'
      });
      handleCloseCourseLockDialog();
      loadUsers();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to update Coming Soon courses. Please try again.',
        severity: 'error'
      });
    } finally {
      setSavingLockedCourses(false);
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1, color: 'primary.main' }}>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage users across all roles
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadUsers}
            disabled={loading}
            sx={{ borderRadius: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={exportUsers}
            disabled={filteredUsers.length === 0}
            sx={{ borderRadius: 2 }}
          >
            Export CSV
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{ borderRadius: 2, px: 3 }}
          >
            Add New User
          </Button>
        </Stack>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3, boxShadow: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }} color="text.primary">
              Filters & Search
            </Typography>
          </Box>
          
          {/* Clear Filters Button */}
          {(searchTerm || roleFilter !== 'all') && (
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              variant="outlined"
              color="warning"
              sx={{ 
                borderRadius: 2,
                whiteSpace: 'nowrap',
                height: 36,
                fontSize: '0.75rem',
                px: 2,
                '&:hover': {
                  backgroundColor: 'warning.light'
                }
              }}
            >
              CLEAR FILTERS
            </Button>
          )}
        </Box>
        
        {/* Main Filters Row */}
        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 3 },
          alignItems: { xs: 'stretch', sm: 'center' },
          mb: 3
        }}>
          {/* Search Field */}
          <Box sx={{ 
            flex: { xs: '1 1 100%', sm: '1 1 40%', md: '1 1 50%' },
            minWidth: { xs: 'auto', sm: '280px' }
          }}>
            <TextField
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              fullWidth
              size="medium"
              sx={{ 
                '& .MuiOutlinedInput-root': { 
                  borderRadius: 3,
                  backgroundColor: 'action.hover',
                  height: 48,
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                } 
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          
          {/* Role Filter */}
          <Box sx={{ 
            flex: { xs: '1 1 100%', sm: '1 1 30%', md: '1 1 25%' },
            minWidth: { xs: 'auto', sm: '200px' }
          }}>
            <FormControl fullWidth size="medium">
              <InputLabel>Filter by Role</InputLabel>
              <Select
                value={roleFilter}
                label="Filter by Role"
                onChange={(e) => setRoleFilter(e.target.value)}
                sx={{ 
                  borderRadius: 3,
                  backgroundColor: 'action.hover',
                  height: 48,
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                }}
              >
                <MenuItem value="all">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PersonIcon fontSize="small" />
                    All Roles
                  </Box>
                </MenuItem>
                <MenuItem value="installer">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BuildIcon fontSize="small" />
                    Installer
                  </Box>
                </MenuItem>
                <MenuItem value="engineer">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EngineeringIcon fontSize="small" />
                    Engineer
                  </Box>
                </MenuItem>
                <MenuItem value="architect">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ArchitectureIcon fontSize="small" />
                    Architect
                  </Box>
                </MenuItem>
                <MenuItem value="salesperson">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SellIcon fontSize="small" />
                    Sales
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Pagination Controls Compact */}
          <Box sx={{ 
            flex: { xs: '1 1 100%', sm: '1 1 30%', md: '1 1 25%' },
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'stretch', sm: 'center' },
            gap: { xs: 1, sm: 1.5 },
            justifyContent: { xs: 'stretch', sm: 'flex-end' }
          }}>
            {/* Per Page Selector */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              justifyContent: { xs: 'space-between', sm: 'flex-end' }
            }}>
              <Typography variant="body2" color="text.secondary" sx={{ 
                fontWeight: 'medium', 
                whiteSpace: 'nowrap',
                fontSize: '0.875rem'
              }}>
                Per page
              </Typography>
              
              <FormControl size="small" sx={{ minWidth: 70 }}>
                <Select
                  value={rowsPerPage}
                  onChange={(e) => {
                    setRowsPerPage(parseInt(e.target.value, 10));
                    setPage(0);
                  }}
                  sx={{ 
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    height: 36,
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'divider',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                    },
                    '& .MuiSelect-select': {
                      py: 0.5,
                      px: 1,
                      fontSize: '0.875rem',
                      fontWeight: 'medium',
                      color: 'text.primary'
                    }
                  }}
                  displayEmpty
                  renderValue={(value) => value}
                >
                  <MenuItem value={5}>5</MenuItem>
                  <MenuItem value={10}>10</MenuItem>
                  <MenuItem value={25}>25</MenuItem>
                  <MenuItem value={50}>50</MenuItem>
                  <MenuItem value={100}>100</MenuItem>
                </Select>
              </FormControl>
            </Box>

            {/* Navigation Controls */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              justifyContent: { xs: 'center', sm: 'flex-end' }
            }}>
              <Tooltip title="First page">
                <span>
                  <IconButton 
                    onClick={() => setPage(0)}
                    disabled={page === 0}
                    size="small"
                    sx={{ 
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      color: 'text.primary',
                      '&:hover': { 
                        backgroundColor: 'primary.light',
                        color: 'primary.main' 
                      },
                      '&.Mui-disabled': {
                        color: 'text.disabled'
                      }
                    }}
                  >
                    <FirstPageIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
              
              <Tooltip title="Previous page">
                <span>
                  <IconButton 
                    onClick={() => setPage(page - 1)}
                    disabled={page === 0}
                    size="small"
                    sx={{ 
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      color: 'text.primary',
                      '&:hover': { 
                        backgroundColor: 'primary.light',
                        color: 'primary.main' 
                      },
                      '&.Mui-disabled': {
                        color: 'text.disabled'
                      }
                    }}
                  >
                    <ChevronLeftIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
              
              <Box sx={{ 
                px: 1.5, 
                py: 0.5, 
                backgroundColor: 'action.hover', 
                borderRadius: 1,
                minWidth: { xs: 80, sm: 70 },
                textAlign: 'center'
              }}>
                <Typography variant="body2" color="text.primary" sx={{ 
                  fontWeight: 'medium', 
                  fontSize: '0.75rem', 
                  whiteSpace: 'nowrap' 
                }}>
                  {page + 1} / {Math.ceil(stats.total / rowsPerPage) || 1}
                </Typography>
              </Box>
              
              <Tooltip title="Next page">
                <span>
                  <IconButton 
                    onClick={() => setPage(page + 1)}
                    disabled={page >= Math.ceil(stats.total / rowsPerPage) - 1}
                    size="small"
                    sx={{ 
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      color: 'text.primary',
                      '&:hover': { 
                        backgroundColor: 'primary.light',
                        color: 'primary.main' 
                      },
                      '&.Mui-disabled': {
                        color: 'text.disabled'
                      }
                    }}
                  >
                    <ChevronRightIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
              
              <Tooltip title="Last page">
                <span>
                  <IconButton 
                    onClick={() => setPage(Math.ceil(stats.total / rowsPerPage) - 1)}
                    disabled={page >= Math.ceil(stats.total / rowsPerPage) - 1}
                    size="small"
                    sx={{ 
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      color: 'text.primary',
                      '&:hover': { 
                        backgroundColor: 'primary.light',
                        color: 'primary.main' 
                      },
                      '&.Mui-disabled': {
                        color: 'text.disabled'
                      }
                    }}
                  >
                    <LastPageIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Box>
        </Box>

        {/* Results Info Row */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pt: 2,
          borderTop: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ 
            fontWeight: 'medium',
            fontSize: '0.875rem'
          }}>
            Showing {Math.min(page * rowsPerPage + 1, stats.total)} - {Math.min((page + 1) * rowsPerPage, stats.total)} of {stats.total} users
          </Typography>
          
          {/* <Box sx={{
            display: 'flex',
            gap: 2,
            alignItems: 'center'
          }}>
            <Chip 
              icon={<BuildIcon />} 
              label={`${stats.installer || 0} Installers`} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
            <Chip 
              icon={<EngineeringIcon />} 
              label={`${stats.engineer || 0} Engineers`} 
              size="small" 
              color="warning" 
              variant="outlined" 
            />
            <Chip 
              icon={<ArchitectureIcon />} 
              label={`${stats.architect || 0} Architects`} 
              size="small" 
              color="info" 
              variant="outlined" 
            />
            <Chip 
              icon={<SellIcon />} 
              label={`${stats.salesperson || 0} Sales`} 
              size="small" 
              color="success" 
              variant="outlined" 
            />
          </Box> */}
        </Box>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper} sx={{ borderRadius: 3, boxShadow: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'action.hover' }}>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>User</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Email</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Role</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Created At</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Updated At</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Loading users...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                  <PersonIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No users found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm || roleFilter !== 'all' 
                      ? 'Try adjusting your search filters'
                      : 'Start by adding your first user'
                    }
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.uid} hover sx={{ 
                  '&:hover': { 
                    backgroundColor: 'action.hover' 
                  } 
                }}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ 
                        bgcolor: `${getRoleColor(user.role)}.main`,
                        color: `${getRoleColor(user.role)}.contrastText`
                      }}>
                        {(user.displayName || user.email || 'U').charAt(0).toUpperCase()}
                      </Avatar>
                      <Box>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }} color="text.primary">
                          {user.displayName || 'No Name'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          UID: {user.uid?.substring(0, 8)}...
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <EmailIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.primary">{user.email}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getRoleIcon(user.role)}
                      label={user.role?.charAt(0).toUpperCase() + user.role?.slice(1)}
                      color={getRoleColor(user.role)}
                      size="small"
                      variant="filled"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.primary">{formatDate(user.createdAt)}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.primary">{formatDate(user.updatedAt)}</Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="More actions">
                      <IconButton
                        onClick={(e) => handleOpenMenu(e, user)}
                        size="small"
                        sx={{ 
                          '&:hover': { 
                            backgroundColor: 'action.hover',
                            color: 'primary.main' 
                          } 
                        }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => handleOpenDialog(selectedUser)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit User</ListItemText>
        </MenuItem>
        {/* <MenuItem onClick={() => console.log('View details', selectedUser)}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem> */}
        <MenuItem 
          onClick={() => handleOpenDeleteDialog(selectedUser)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete User</ListItemText>
        </MenuItem>
        <MenuItem 
          onClick={() => handleOpenCourseLockDialog(selectedUser)}
          sx={{ color: 'info.main' }}
        >
          <ListItemIcon>
            <CategoryIcon fontSize="small" color="info" />
          </ListItemIcon>
          <ListItemText>Lock Courses</ListItemText>
        </MenuItem>
      </Menu>

      {/* Add/Edit User Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Display Name"
              value={formData.displayName}
              onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              fullWidth
              required
            />
            {!editingUser && (
              <TextField
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                fullWidth
                required
                helperText="Minimum 6 characters"
              />
            )}
            <TextField
              select
              label="Role"
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              fullWidth
              required
            >
              <MenuItem value="installer">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <BuildIcon fontSize="small" />
                  Installer
                </Box>
              </MenuItem>
              <MenuItem value="engineer">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <EngineeringIcon fontSize="small" />
                  Engineer
                </Box>
              </MenuItem>
              <MenuItem value="architect">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ArchitectureIcon fontSize="small" />
                  Architect
                </Box>
              </MenuItem>
              <MenuItem value="salesperson">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SellIcon fontSize="small" />
                  Sales
                </Box>
              </MenuItem>
            </TextField>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveUser} 
            variant="contained"
            disabled={!formData.email || !formData.displayName || (!editingUser && !formData.password)}
          >
            {editingUser ? 'Update User' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-confirmation-dialog"
      >
        <DialogTitle id="delete-confirmation-dialog">
          Confirm Deletion
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Are you sure you want to delete this user? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmDelete} 
            color="error" 
            variant="contained"
          >
            Delete User
          </Button>
        </DialogActions>
      </Dialog>

      {/* Course Lock Confirmation Dialog */}
      <Dialog
        open={openCourseLockDialog}
        onClose={handleCloseCourseLockDialog}
        aria-labelledby="course-lock-confirmation-dialog"
      >
        <DialogTitle id="course-lock-confirmation-dialog">
          {selectedUserForCourses ? `Lock Courses for ${selectedUserForCourses.displayName}` : 'Lock Courses'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {selectedUserForCourses ? `Manage course locks for ${selectedUserForCourses.displayName}` : 'Select courses to lock or unlock'}
          </Typography>
          
          {/* Courses List */}
          <Divider sx={{ my: 2 }} />
          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
            {coursesLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <List>
                {courses.map((course) => {
                  const isComingSoon = userLockedCourses.includes(course.id);
                  return (
                    <ListItem key={course.id} disablePadding>
                      <ListItemText primary={course.name} />
                      <ListItemSecondaryAction>
                        {courseLockLoading[course.id] ? (
                          <CircularProgress size={24} />
                        ) : (
                          <IconButton
                            edge="end"
                            onClick={() => handleCourseLockToggle(course.id)}
                            size="small"
                            sx={{ 
                              color: isComingSoon ? 'error.main' : 'success.main',
                              '&:hover': { 
                                backgroundColor: isComingSoon ? 'error.lighter' : 'success.lighter',
                              }
                            }}
                            disabled={!!courseLockLoading[course.id]}
                          >
                            {isComingSoon ? <LockIcon /> : <LockOpenIcon />}
                          </IconButton>
                        )}
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCourseLockDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveLockedCourses}
            variant="contained"
            disabled={savingLockedCourses}
            startIcon={savingLockedCourses ? <CircularProgress size={18} /> : null}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default UserManagement;