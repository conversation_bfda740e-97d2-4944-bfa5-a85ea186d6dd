import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Fab,
  RadioGroup,
  Radio,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as VideoIcon,
  Subtitles as AnnotationIcon,
  AccessTime as TimelineIcon,
  LiveHelp as TutorialIcon,
  CloudUpload,
  Link as LinkIcon,
} from '@mui/icons-material';

import adminModuleService from '../services/adminModuleService';
import { convertToSignedUrl } from '../utils/wasabiHelper';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`video-tabpanel-${index}`}
      aria-labelledby={`video-tab-${index}`}
      style={{ padding: '16px 0' }}
      {...other}
    >
      {value === index && (
        <Box>{children}</Box>
      )}
    </div>
  );
}

// Video Player Component - Enhanced to support both files and embed URLs
function VideoPlayer({ videoUrl, videoAnnotations = [], currentTime = 0, onTimeUpdate, onAnnotationClick }) {
  const videoRef = useRef(null);
  const iframeRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);

  // Detect if URL is an embed URL (YouTube, Vimeo, etc.)
  const isEmbedUrl = (url) => {
    if (!url) return false;
    return url.includes('youtube.com/embed') || 
           url.includes('youtu.be') || 
           url.includes('vimeo.com') ||
           url.includes('player.vimeo.com');
  };

  // Convert various video URLs to proper embed format
  const getEmbedUrl = (url) => {
    if (!url) return null;
    
    // YouTube URLs
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1]?.split('&')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1]?.split('?')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    
    // Vimeo URLs
    if (url.includes('vimeo.com/') && !url.includes('player.vimeo.com')) {
      const videoId = url.split('vimeo.com/')[1]?.split('?')[0];
      return `https://player.vimeo.com/video/${videoId}`;
    }
    if (url.includes('player.vimeo.com/video/')) {
      // Already in embed format, but ensure it's complete
      const videoId = url.split('video/')[1]?.split('?')[0];
      return `https://player.vimeo.com/video/${videoId}`;
    }
    
    // Return as-is for direct video files or already proper embed URLs
    return url;
  };

  const isEmbed = isEmbedUrl(videoUrl);
  const embedUrl = isEmbed ? getEmbedUrl(videoUrl) : null;

  useEffect(() => {
    if (!isEmbed) {
      const video = videoRef.current;
      if (!video) return;

      const handleTimeUpdate = () => {
        if (onTimeUpdate) {
          onTimeUpdate(video.currentTime);
        }
      };

      const handleLoadedMetadata = () => {
        setDuration(video.duration);
      };

      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('loadedmetadata', handleLoadedMetadata);

      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      };
    }
  }, [onTimeUpdate, isEmbed]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
      {videoUrl ? (
        <>
          {isEmbed ? (
            // Render iframe for embed URLs (YouTube, Vimeo, etc.)
            <iframe
              ref={iframeRef}
              src={embedUrl}
              width="100%"
              height="100%"
              style={{
                border: 'none',
                borderRadius: '8px',
                // backgroundColor: '#000'
              }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              title="Video Player"
            />
          ) : (
            // Render video element for direct video files
            <video
              ref={videoRef}
              src={videoUrl}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                borderRadius: '8px'
                // backgroundColor: '#000'
              }}
              controls
            />
          )}
          
          {/* Video Annotations Overlay - Only show for direct video files */}
          {!isEmbed && videoAnnotations.map((annotation, index) => {
            const isActive = currentTime >= annotation.startTime && currentTime <= annotation.endTime;
            if (!isActive) return null;

            return (
              <Box
                key={annotation.id || index}
                sx={{
                  position: 'absolute',
                  left: `${annotation.position.x}%`,
                  top: `${annotation.position.y}%`,
                  transform: 'translate(-50%, -50%)',
                  zIndex: 10
                }}
              >
                <Button
                  variant="contained"
                  size="small"
                  color="primary"
                  onClick={() => onAnnotationClick?.(annotation)}
                  sx={{
                    minWidth: 'auto',
                    borderRadius: '50%',
                    width: 32,
                    height: 32,
                    animation: 'pulse 2s infinite'
                  }}
                >
                  {index + 1}
                </Button>
              </Box>
            );
          })}
          
          {/* Show info for embed videos */}
          {isEmbed && (
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.7)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white' }}>
                Embed Video
              </Typography>
            </Box>
          )}
        </>
      ) : (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#f5f5f5',
            border: '2px dashed #ccc',
            cursor: 'pointer'
          }}
        >
          <CloudUpload sx={{ fontSize: 60, color: '#999', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            Upload Video File
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Drag and drop or click to select video
          </Typography>
        </Box>
      )}
    </Box>
  );
}

// Loading component
function LoadingOverlay() {
  return (
    <Box sx={{ 
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        Loading Video...
      </Typography>
    </Box>
  );
}

function VideoStepEditor() {
  const location = useLocation();
  const navigate = useNavigate();
  const { moduleId, stepId, moduleData } = location.state || {};
  
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [videoUrl, setVideoUrl] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [currentTime, setCurrentTime] = useState(0);
  const fileInputRef = useRef(null);

  // Video source options
  const [videoSource, setVideoSource] = useState('upload'); // 'upload' or 'url'
  const [videoUrlInput, setVideoUrlInput] = useState('');

  // Enhanced video file state để track uploaded video
  const [videoFile, setVideoFile] = useState(null);
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState(''); // Store uploaded video URL
  const [originalVideoUrl, setOriginalVideoUrl] = useState(''); // Store original video URL from server

  // Step data state
  const [stepData, setStepData] = useState({
    title: '',
    content: '',
    // Remove order initialization - let backend auto-calculate
    autoPlay: false,
    tags: [],
    stepType: 'video'
  });

  // Video-specific state
  const [videoAnnotations, setVideoAnnotations] = useState([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);

  // Load existing step data if editing
  useEffect(() => {
    if (!moduleId) {
      navigate('/modules');
      return;
    }
    
    const loadStepData = async () => {
      setLoading(true);
      try {
        if (stepId) {
          const stepResponse = await adminModuleService.getStep(moduleId, stepId);
          if (stepResponse) {
            setStepData({
              title: stepResponse.title || '',
              content: stepResponse.content || '',
              order: stepResponse.order || 1,
              autoPlay: stepResponse.autoPlay || false,
              tags: stepResponse.tags || [],
              stepType: 'video'
            });
            
            if (stepResponse.videoUrl) {
              console.log('📹 Loading existing video URL:', stepResponse.videoUrl);
              
              // Check if it's a Wasabi URL that needs signed URL conversion
              const isWasabiUrl = stepResponse.videoUrl.includes('wasabisys.com') || 
                                  stepResponse.videoUrl.includes('airsmart') || 
                                  !stepResponse.videoUrl.includes('://');
              
              if (isWasabiUrl) {
                // This is an uploaded file
                setVideoSource('upload');
                const signedUrl = convertToSignedUrl(stepResponse.videoUrl);
                setVideoUrl(signedUrl);
                setVideoUrlInput(''); // Clear URL input for uploaded files
                console.log('📹 Set as upload with signed URL:', signedUrl);
              } else {
                // This is an external URL (YouTube, Vimeo, etc.)
                setVideoSource('url');
                setVideoUrlInput(stepResponse.videoUrl); // Set the input field
                setVideoUrl(stepResponse.videoUrl); // Set the preview URL
                console.log('📹 Set as URL source:', stepResponse.videoUrl);
              }
            }
            
            if (stepResponse.videoAnnotations && Array.isArray(stepResponse.videoAnnotations)) {
              setVideoAnnotations(stepResponse.videoAnnotations);
            }
          }
        }
      } catch (error) {
        console.error('Error loading topic data:', error);
        setSnackbar({
          open: true,
          message: `Error loading data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadStepData();
  }, [moduleId, stepId, navigate]);

  // Handle video file selection
  const handleVideoFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
    if (!validTypes.includes(file.type)) {
      setSnackbar({
        open: true,
        message: 'Please select a valid video file (MP4, WebM, OGG, AVI, MOV)',
        severity: 'error'
      });
      return;
    }

    // Check file size (limit to 500MB)
    const maxSize = 500 * 1024 * 1024; // 500MB
    if (file.size > maxSize) {
      setSnackbar({
        open: true,
        message: 'Video file is too large. Maximum size is 500MB.',
        severity: 'error'
      });
      return;
    }

    setVideoFile(file);
    const newVideoUrl = URL.createObjectURL(file);
    setVideoUrl(newVideoUrl);
    setUploadedVideoUrl(newVideoUrl); // Store new uploaded video URL
    setVideoUrlInput(''); // Clear URL input when file is selected
    
    // Reset file input để có thể select lại cùng file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    
    console.log('Video file selected:', file.name, 'Size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
  };

  // Handle video URL input
  const handleVideoUrlChange = (url) => {
    setVideoUrlInput(url);
    if (url.trim()) {
      setVideoUrl(url.trim());
      setVideoFile(null); // Clear file if URL is provided
      console.log('Video URL updated:', url.trim());
    } else {
      // Clear video URL if input is empty
      setVideoUrl('');
    }
  };

  // Handle video source change
  const handleVideoSourceChange = (event) => {
    const newSource = event.target.value;
    setVideoSource(newSource);
    
    if (newSource === 'upload') {
      // Clear URL input and video URL when switching to upload
      setVideoUrlInput('');
      setVideoUrl('');
      // Note: Don't clear videoFile here - user might want to keep their uploaded file
      // If they want to change the file, they can use the file picker button
    } else if (newSource === 'url') {
      // Clear video file when switching to URL
      setVideoFile(null);
      // If user has URL input, use it; otherwise clear video URL
      if (videoUrlInput.trim()) {
        setVideoUrl(videoUrlInput.trim());
      } else {
        setVideoUrl('');
      }
    }
  };

  // Handle video click to add annotation
  const handleVideoClick = (event) => {
    if (tabIndex !== 1) return; // Only add annotations on Annotations tab

    const rect = event.currentTarget.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    const newAnnotation = {
      id: `annotation_${Date.now()}`,
      position: { x, y },
      startTime: currentTime,
      endTime: currentTime + 5, // Default 5 second duration
      title: `Annotation ${videoAnnotations.length + 1}`,
      description: '',
      action: 'info'
    };

    setVideoAnnotations([...videoAnnotations, newAnnotation]);
    setSelectedAnnotation(newAnnotation);
  };

  // Handle save step - Enhanced với upload video
  const handleSaveStep = async () => {
    try {
      setUploading(true);
      setUploadProgress(0);

      // Validate required fields
      if (!stepData.title.trim()) {
        throw new Error('Topic title is required');
      }
      if (!stepData.content.trim()) {
        throw new Error('Topic content is required');
      }
      if (!videoUrl && !videoFile && !videoUrlInput.trim()) {
        throw new Error('Please provide a video file or URL');
      }

      console.log('🔄 Saving video topic...');

      let result;

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      if (videoFile && videoSource === 'upload') {
        // If there's a video file to upload
        const formData = new FormData();
        formData.append('video', videoFile);
        formData.append('stepData', JSON.stringify({
          ...stepData,
          // Don't include order for new steps - let backend auto-calculate
          videoAnnotations: videoAnnotations
        }));

        if (stepId) {
          // Update existing step with new video
          result = await adminModuleService.updateStepWithVideo(moduleId, stepId, formData);
        } else {
          // Create new step with video
          result = await adminModuleService.createStepWithVideo(moduleId, formData);
        }
      } else {
        // No video file, use URL or existing video
        const completeStepData = {
          ...stepData,
          // Don't include order for new steps - let backend auto-calculate
          videoUrl: videoSource === 'url' ? videoUrlInput.trim() : videoUrl,
          videoAnnotations: videoAnnotations
        };

        if (stepId) {
          result = await adminModuleService.updateStep(moduleId, stepId, completeStepData);
        } else {
          result = await adminModuleService.createStep(moduleId, completeStepData);
        }
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      console.log('✅ Video topic saved successfully:', result);

      setSnackbar({
        open: true,
        message: stepId ? 'Video topic updated successfully!' : 'Video topic created successfully!',
        severity: 'success'
      });
      
      setTimeout(() => {
        navigate('/modules');
      }, 1500);

    } catch (error) {
      console.error('❌ Error saving video topic:', error);
      setSnackbar({
        open: true,
        message: `Error saving topic: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <Box sx={{ height: 'calc(100vh - 88px)', overflow: 'hidden' }}>
      {/* Header/Toolbar */}
      <Paper sx={{ mb: 2 }} elevation={2}>
        <Toolbar>
          <IconButton edge="start" color="inherit" onClick={() => navigate('/modules')}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            {stepId ? 'Edit Video Topic' : 'Create New Video Topic'}
          </Typography>
          <Button 
            variant="contained"
            color="success"
            startIcon={<Save />}
            onClick={handleSaveStep}
            disabled={loading || !stepData.title || !stepData.content}
          >
            Save Topic
          </Button>
        </Toolbar>
      </Paper>

      {/* Main Content */}
      <Box sx={{ 
        display: 'flex', 
        height: 'calc(100% - 80px)', 
        gap: 1,
        px: 2
      }}>
        {/* Video Preview Panel - Left Side */}
        <Box sx={{ 
          flex: '1 1 75%',
          height: '100%',
          minWidth: 0
        }}>
          <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={3}>
            {loading && !videoUrl && <LoadingOverlay />}
            
            {/* Video Player */}
            <Box 
              sx={{ height: '100%', width: '100%' }}
              onClick={handleVideoClick}
            >
              <VideoPlayer
                videoUrl={videoUrl}
                videoAnnotations={videoAnnotations}
                currentTime={currentTime}
                onTimeUpdate={setCurrentTime}
                onAnnotationClick={setSelectedAnnotation}
              />
            </Box>
            
            {/* Video Controls Toolbar */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}>
              <Tooltip title="Upload Video">
                <Fab 
                  size="small" 
                  color="primary" 
                  onClick={() => fileInputRef.current?.click()}
                >
                  <VideoIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Add Annotation (Click on video)">
                <Fab size="small" color="secondary">
                  <AnnotationIcon />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Video Info */}
            {/* <Box sx={{ 
              position: 'absolute', 
              bottom: 16, 
              left: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Current Time: {Math.floor(currentTime / 60)}:{(Math.floor(currentTime % 60)).toString().padStart(2, '0')}
              </Typography>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Annotations: {videoAnnotations.length}
              </Typography>
            </Box> */}

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              style={{ display: 'none' }}
              onChange={handleVideoFileSelect}
            />
          </Paper>
        </Box>

        {/* Step Editor Panel - Right Side */}
        <Box sx={{ 
          flex: '1 1 25%',
          height: '100%',
          minWidth: '320px',
          maxWidth: '400px'
        }}>
          <Paper sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} elevation={3}>
            {/* Tabs Navigation */}
            <AppBar position="static" color="default" elevation={0} sx={{ flexShrink: 0 }}>
              <Tabs
                value={tabIndex}
                onChange={(e, newValue) => setTabIndex(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',
                    fontSize: '0.75rem',
                    padding: '6px 4px'
                  }
                }}
              >
                <Tab label="Basic" icon={<VideoIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Annotations" icon={<AnnotationIcon fontSize="small" />} iconPosition="top" />
                {/* Tab 3: Actions - Disabled */}
                <Tab label="Actions" icon={<TimelineIcon fontSize="small" />} iconPosition="top" disabled />
                {/* Tab 4: Interactive Tutorial - Disabled */}
                <Tab label="Tutorial" icon={<TutorialIcon fontSize="small" />} iconPosition="top" disabled />
              </Tabs>
            </AppBar>

            {/* Tab Content */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
              {/* Tab 1: Basic Information */}
              <TabPanel value={tabIndex} index={0}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                  <TextField
                    label="Topic Title"
                    value={stepData.title}
                    onChange={(e) => setStepData({ ...stepData, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                    disabled={uploading}
                  />
                  
                  <TextField
                    label="Content"
                    value={stepData.content}
                    onChange={(e) => setStepData({ ...stepData, content: e.target.value })}
                    multiline
                    rows={3}
                    fullWidth
                    required
                    placeholder="Detailed topic instructions. This will be displayed to the user."
                    size="small"
                    disabled={uploading}
                  />

                  {/* <Grid container spacing={1}>
                    
                    <Grid item xs={12}>
                      <TextField
                        label="Duration (sec)"
                        type="number"
                        value={stepData.duration}
                        onChange={(e) => setStepData({ ...stepData, duration: parseInt(e.target.value) || 30 })}
                        fullWidth
                        size="small"
                        disabled={uploading}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">sec</InputAdornment>,
                        }}
                      />
                    </Grid>
                  </Grid> */}

                  <TextField
                    label="Tags (comma separated)"
                    value={stepData.tags ? stepData.tags.join(', ') : ''}
                    onChange={(e) => setStepData({
                      ...stepData,
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    fullWidth
                    placeholder="e.g. video, training, important"
                    size="small"
                    disabled={uploading}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={stepData.autoPlay || false}
                        onChange={(e) => setStepData({ ...stepData, autoPlay: e.target.checked })}
                        size="small"
                        disabled={uploading}
                      />
                    }
                    label="Auto-play video when topic is loaded"
                  />
                  
                      <Divider sx={{ my: 1 }} />
                  
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>Video Source</Typography>
                  
                  <FormControl component="fieldset" disabled={uploading}>
                    <RadioGroup
                      row
                      value={videoSource}
                      onChange={handleVideoSourceChange}
                    >
                      <FormControlLabel value="upload" control={<Radio size="small" />} label="Upload File" />
                      <FormControlLabel value="url" control={<Radio size="small" />} label="Video URL" />
                    </RadioGroup>
                  </FormControl>

                  {videoSource === 'upload' && (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Button
                        variant="outlined"
                        startIcon={<CloudUpload />}
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading}
                        sx={{ alignSelf: 'flex-start' }}
                      >
                        {videoFile ? 'Change Video File' : 'Select Video File'}
                      </Button>
                      
                      {videoFile && (
                        <Alert severity="success" sx={{ mt: 1 }}>
                          <Typography variant="body2">
                            <strong>Selected:</strong> {videoFile.name}
                          </Typography>
                          <Typography variant="caption">
                            Size: {(videoFile.size / 1024 / 1024).toFixed(2)} MB
                          </Typography>
                        </Alert>
                      )}
                      
                      <Typography variant="caption" color="text.secondary">
                        Supported formats: MP4, WebM, OGG, AVI, MOV (Max: 500MB)
                      </Typography>
                    </Box>
                  )}

                  {videoSource === 'url' && (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <TextField
                        label="Video URL"
                        value={videoUrlInput}
                        onChange={(e) => handleVideoUrlChange(e.target.value)}
                        fullWidth
                        size="small"
                        disabled={uploading}
                        placeholder="https://example.com/video.mp4 or YouTube/Vimeo URL"
                        InputProps={{
                          startAdornment: <InputAdornment position="start"><LinkIcon /></InputAdornment>,
                        }}
                      />
                      
                      <Typography variant="caption" color="text.secondary">
                        Enter a direct video URL or embed URL from YouTube, Vimeo, etc.
                      </Typography>
                    </Box>
                  )}
                  
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="caption">
                      💡 Click on the video area to add annotations. Switch to the "Annotations" tab to edit them.
                      </Typography>
                      </Alert>
                </Box>
              </TabPanel>
              
              {/* Tab 2: Video Annotations */}
              <TabPanel value={tabIndex} index={1}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Annotations ({videoAnnotations.length})
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        const newAnnotation = {
                          id: `annotation_${Date.now()}`,
                          position: { x: 50, y: 50 },
                          startTime: currentTime,
                          endTime: currentTime + 5,
                          title: `Annotation ${videoAnnotations.length + 1}`,
                          description: '',
                          action: 'info'
                        };
                        setVideoAnnotations([...videoAnnotations, newAnnotation]);
                        setSelectedAnnotation(newAnnotation);
                      }}
                    >
                      Add Annotation
                    </Button>
                  </Box>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Click on the video to add annotations. They will appear during the specified time range.
                  </Alert>
                  
                  <List>
                    {videoAnnotations.map((annotation, index) => (
                      <ListItem 
                        key={annotation.id || index}
                        button
                        selected={selectedAnnotation?.id === annotation.id}
                        onClick={() => setSelectedAnnotation(annotation)}
                      >
                        <ListItemText 
                          primary={annotation.title}
                          secondary={`${Math.floor(annotation.startTime)}s - ${Math.floor(annotation.endTime)}s`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => {
                              setVideoAnnotations(videoAnnotations.filter(a => a.id !== annotation.id));
                              if (selectedAnnotation?.id === annotation.id) {
                                setSelectedAnnotation(null);
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  
                  {videoAnnotations.length === 0 && (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
                      <Typography color="text.secondary">
                        No annotations added yet. Click on the video to add one.
                      </Typography>
                    </Box>
                  )}
                  
                  {selectedAnnotation && (
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Edit Annotation
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                          <TextField
                            label="Title"
                            value={selectedAnnotation.title}
                            onChange={(e) => {
                              const updatedAnnotations = videoAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, title: e.target.value } 
                                  : a
                              );
                              setVideoAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, title: e.target.value });
                            }}
                            fullWidth
                            size="small"
                          />
                          
                          <TextField
                            label="Description"
                            value={selectedAnnotation.description || ''}
                            onChange={(e) => {
                              const updatedAnnotations = videoAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, description: e.target.value } 
                                  : a
                              );
                              setVideoAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, description: e.target.value });
                            }}
                            multiline
                            rows={3}
                            fullWidth
                            size="small"
                          />
                          
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <TextField
                                label="Start Time (s)"
                                type="number"
                                value={selectedAnnotation.startTime}
                                onChange={(e) => {
                                  const startTime = Math.max(0, parseFloat(e.target.value) || 0);
                                  const updatedAnnotations = videoAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, startTime } 
                                      : a
                                  );
                                  setVideoAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ ...selectedAnnotation, startTime });
                                }}
                                fullWidth
                                size="small"
                              />
                            </Grid>
                            <Grid item xs={6}>
                              <TextField
                                label="End Time (s)"
                                type="number"
                                value={selectedAnnotation.endTime}
                                onChange={(e) => {
                                  const endTime = Math.max(selectedAnnotation.startTime, parseFloat(e.target.value) || 0);
                                  const updatedAnnotations = videoAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, endTime } 
                                      : a
                                  );
                                  setVideoAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ ...selectedAnnotation, endTime });
                                }}
                                fullWidth
                                size="small"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </TabPanel>
              
              {/* Tab 3: Actions - Disabled */}
              <TabPanel value={tabIndex} index={2}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Actions functionality has been disabled
                </Typography>
              </TabPanel>
              
              {/* Tab 4: Interactive Tutorial - Disabled */}
              <TabPanel value={tabIndex} index={3}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Interactive tutorial functionality has been disabled
                </Typography>
              </TabPanel>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Upload Progress */}
      {uploading && (
        <Box sx={{ 
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          background: 'rgba(255, 255, 255, 0.95)',
          padding: 3,
          borderRadius: 2,
          minWidth: '300px',
          textAlign: 'center',
          boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
        }}>
          <Typography variant="h6" gutterBottom>
            {stepId ? 'Updating Video Topic...' : 'Creating Video Topic...'}
          </Typography>
          <Typography variant="body2" gutterBottom sx={{ mb: 2 }}>
            {videoFile ? `Uploading ${videoFile.name}...` : 'Processing topic data...'}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={uploadProgress} 
            sx={{ mb: 2, height: 8, borderRadius: 4 }}
          />
          <Typography variant="body2" color="text.secondary">
            {uploadProgress}% complete
          </Typography>
        </Box>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        style={{ display: 'none' }}
        onChange={handleVideoFileSelect}
        disabled={uploading}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default VideoStepEditor;