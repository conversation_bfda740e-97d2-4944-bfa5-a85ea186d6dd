/* eslint-disable @typescript-eslint/no-unsafe-call */
import {
  IsString,
  IsOptional,
  IsNumber,
  IsArray,
  IsBoolean,
  IsUrl,
  MaxLength,
  <PERSON><PERSON>ength,
  <PERSON>,
} from 'class-validator';

export class CreateCourseDto {
  @IsString()
  @MinLength(1, { message: 'Course name is required' })
  @MaxLength(100, { message: 'Course name must be less than 100 characters' })
  name: string;

  @IsString()
  @MinLength(1, { message: 'Course description is required' })
  @MaxLength(1000, { message: 'Course description must be less than 1000 characters' })
  description: string;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Order must be at least 1' })
  order?: number; // Display order

  @IsOptional()
  @IsUrl({}, { message: 'Thumbnail must be a valid URL' })
  thumbnail?: string; // Course thumbnail image

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsBoolean()
  isActive?: boolean; // Course active status (default: true)

  @IsOptional()
  @IsBoolean()
  isComingSoon?: boolean; // Lock course access (default: false)


}
