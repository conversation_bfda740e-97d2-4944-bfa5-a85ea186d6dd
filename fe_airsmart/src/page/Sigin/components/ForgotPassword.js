import * as React from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import TextField from '@mui/material/TextField';
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';

// API service for password reset
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';

const sendPasswordResetEmail = async (email) => {
  const response = await fetch(`${API_BASE_URL}auth/forgot-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || 'Failed to send password reset email');
  }

  return data;
};

function ForgotPassword({ open, handleClose }) {
  const [email, setEmail] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [success, setSuccess] = React.useState(false);
  const [error, setError] = React.useState('');
  const [emailError, setEmailError] = React.useState(false);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    // Reset states
    setError('');
    setEmailError(false);
    
    // Validate email
    if (!email || !validateEmail(email)) {
      setEmailError(true);
      setError('Please enter a valid email address.');
      return;
    }

    try {
      setLoading(true);
      await sendPasswordResetEmail(email);
      setSuccess(true);
    } catch (err) {
      setError(err.message || 'Failed to send password reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setEmail('');
    setError('');
    setSuccess(false);
    setEmailError(false);
    setLoading(false);
  };

  const handleCloseDialog = () => {
    handleReset();
    handleClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleCloseDialog}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        Reset Password
      </DialogTitle>
      <DialogContent>
        {success ? (
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Alert severity="success" sx={{ mb: 2 }}>
              <strong>Email Sent Successfully!</strong>
            </Alert>
            <DialogContentText sx={{ mb: 2 }}>
              If an account with <strong>{email}</strong> exists, we've sent a password reset link to your email.
            </DialogContentText>
            <DialogContentText variant="body2" color="text.secondary">
              Please check your inbox and spam folder. The link will expire in 1 hour.
            </DialogContentText>
          </Box>
        ) : (
          <Box component="form" onSubmit={handleSubmit}>
            <DialogContentText sx={{ mb: 3 }}>
              Enter your account email address, and we'll send you a link to reset your password.
            </DialogContentText>
            
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TextField
              autoFocus
              required
              fullWidth
              label="Email Address"
              type="email"
              variant="outlined"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={emailError}
              helperText={emailError ? 'Please enter a valid email address' : ''}
              disabled={loading}
              sx={{ mb: 2 }}
            />
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3 }}>
        {success ? (
          <Button onClick={handleCloseDialog} variant="contained" fullWidth>
            Close
          </Button>
        ) : (
          <>
            <Button 
              onClick={handleCloseDialog} 
              disabled={loading}
              sx={{ 
                color: loading ? 'text.disabled' : 'text.secondary',
                '&:hover': {
                  bgcolor: loading ? 'transparent' : 'action.hover'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={loading}
              sx={{
                minWidth: 140,
                bgcolor: (!email.trim() && !loading) ? 'grey.400' : 'primary.main',
                color: (!email.trim() && !loading) ? 'white' : 'primary.contrastText',
                '&:hover': {
                  bgcolor: (!email.trim() && !loading) ? 'grey.500' : 'primary.dark',
                },
                '&:disabled': {
                  bgcolor: 'grey.300',
                  color: 'grey.600',
                },
                cursor: (!email.trim() && !loading) ? 'not-allowed' : 'pointer',
                fontWeight: 600,
                textTransform: 'none',
              }}
            >
              {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={16} sx={{ color: 'inherit' }} />
                  <Box component="span" sx={{ color: 'inherit' }}>Sending...</Box>
                </Box>
              ) : (
                <Box component="span" sx={{ color: 'inherit' }}>
                  Send Reset Link
                </Box>
              )}
            </Button>

          </>
        )}
      </DialogActions>
    </Dialog>
  );
}

ForgotPassword.propTypes = {
  handleClose: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
};

export default ForgotPassword;